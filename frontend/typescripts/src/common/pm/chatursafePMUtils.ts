import { createDmMessageBubble } from "../../cb/components/pm/dmMessageItem"
import { ReactWrapper } from "../../cb/components/ReactWrapper"
import { swapColors } from "../chatcolors/darkModeColors"
import { renderMessage } from "../renderMessage"
import { createRoomMessage } from "../theatermodelib/messageToDOM"
import type { IPMError } from "../../cb/api/pm"
import type { ITipError } from "../../cb/api/tipping"
import type { IPrivateMessage } from "../messageInterfaces"

export interface ChatursafePMConfig {
    originalMessage: IPrivateMessage
    flaggedCategories: string[]
    messageId: string
    isScrolledUp: () => boolean
    scrollToBottom: () => void
    removeMessage: (messageId: string) => void
    isMobile?: boolean
    isPureChat?: boolean
    enableDarkMode?: boolean
}

/**
 * Creates a unified Chatursafe flagged message for PM tabs across all platforms.
 * This utility handles the common logic for creating flagged messages in:
 * - Mobile Private Tab
 * - Theater Mode PM Tab
 * - Full Video Mode PM Tab
 */
export function createChatursafePMFlaggedMessage(
    config: ChatursafePMConfig,
): HTMLDivElement {
    const {
        originalMessage,
        flaggedCategories,
        messageId,
        isScrolledUp,
        scrollToBottom,
        removeMessage,
        isMobile = false,
        isPureChat = false,
        enableDarkMode = false,
    } = config

    // Create the original message div
    const messageDiv = createRoomMessage(originalMessage)
    messageDiv.classList.add("chtrsf-flagged-chat")
    messageDiv.style.paddingLeft = "0"
    messageDiv.dataset["msgId"] = messageId

    // Apply dark mode colors if enabled (theater mode)
    if (enableDarkMode) {
        swapColors(messageDiv, document.body.classList.contains("darkmode"))
    }

    // Capture scroll state before rendering
    const wasScrolledUp = isScrolledUp()

    // Create the appropriate React wrapper based on platform
    const componentName = isMobile ? "MobileFlaggedChat" : "DesktopFlaggedChat"
    const componentProps: Record<string, unknown> = {
        chatDiv: messageDiv,
        reasons: flaggedCategories,
        scrollCallback: () => {
            if (!wasScrolledUp) {
                scrollToBottom()
            }
        },
        removeCallback: () => {
            removeMessage(messageId)
        },
    }

    // Add isPureChat prop for desktop components when needed
    if (!isMobile && isPureChat) {
        componentProps.isPureChat = true
    }

    const flaggedDiv = new ReactWrapper({
        component: componentName,
        componentProps,
    })

    // Set the flagged ID for DOM queries
    flaggedDiv.element.dataset["chtrsfFlaggedId"] = messageId

    return flaggedDiv.element as HTMLDivElement
}

/**
 * Creates a unified flagged DM message component for both desktop and mobile DM windows.
 * This utility handles the common logic for creating flagged messages with React components.
 */
export function createFlaggedDmMessage(config: {
    error: IPMError
    messageList: HTMLDivElement
    isScrolledUp: () => boolean
    scrollToBottom: () => void
    addMessageToEnd: (
        element: HTMLDivElement,
        isMine: boolean,
        skipJumpToBottom: boolean,
    ) => void
}): void {
    const {
        error,
        messageList,
        isScrolledUp,
        scrollToBottom,
        addMessageToEnd,
    } = config
    if (!error.originalMessage) {
        return
    }
    const messageText = error.originalMessage.message
    const messageContent = renderMessage(
        typeof messageText === "string" ? messageText : "",
    )
    const messageDiv = createDmMessageBubble(
        messageContent,
        true,
        "dmWindowMessage",
        "me",
    )
    messageDiv.classList.add("chtrsf-flagged-dm")
    messageDiv.dataset["msgId"] = String(error.originalMessage.messageID)

    const wasScrolledUp = isScrolledUp()
    const flaggedDiv = new ReactWrapper({
        component: "FlaggedDm",
        componentProps: {
            chatDiv: messageDiv,
            reasons: error.chatursafeFlaggedCategories,
            scrollCallback: () => {
                if (!wasScrolledUp) {
                    scrollToBottom()
                }
            },
            removeCallback: () => {
                const flaggedWrapper = messageList.querySelector(
                    `[data-chtrsf-flagged-id="${String(error.originalMessage?.messageID)}"]`,
                )
                if (flaggedWrapper) {
                    messageList.removeChild(flaggedWrapper)
                }
            },
        },
    })
    flaggedDiv.element.dataset["chtrsfFlaggedId"] = String(
        error.originalMessage.messageID,
    )
    addMessageToEnd(flaggedDiv.element as HTMLDivElement, true, false)
}

interface ChatursafePMError extends IPMError {
    chatursafeFlaggedCategories: string[]
    originalMessage: IPrivateMessage
}

export const isChatursafeFlaggedMessage = (
    error: IPMError,
): error is ChatursafePMError => {
    return Boolean(
        error.chatursafeFlaggedCategories &&
            error.chatursafeFlaggedCategories.length > 0 &&
            error.originalMessage,
    )
}

export interface ChatursafeFlaggedTipError extends ITipError {
    chatursafeFlaggedCategories: string[]
    originalMessage: IPrivateMessage
}

export const isChatursafeFlaggedTipError = (
    error: ITipError | number,
): error is ChatursafeFlaggedTipError => {
    return typeof error === "object" && Boolean(
        error.chatursafeFlaggedCategories &&
        error.chatursafeFlaggedCategories.length > 0 &&
        error.originalMessage,
    )
}
