import { ArgJSONMap } from "@multimediallc/web-utils"
import { getCb, postCb } from "../../common/api"
import { EventRouter } from "../../common/events"
import { pageContext } from "../interfaces/context"
import { addBaseTopicFields } from "../pushservicelib/topics/base"
import { UserMessageTopic } from "../pushservicelib/topics/user"
import type { IPMError } from "./pm"
import type { IPrivateMessage, ITipInPast24HoursNotification, ITokenBalanceUpdateNotification } from "../../common/messageInterfaces"
import type { RoomType } from "../../common/roomUtil"

export const tipsInPast24HoursUpdate = new EventRouter<ITipInPast24HoursNotification>("tipsInPast24HoursUpdate")

export const tokenBalanceUpdate = new EventRouter<ITokenBalanceUpdateNotification>("tokenBalanceUpdate")

export const enum TipType {
    public = "public",
    anonymous = "anonymous",
}

interface ISendTipData {
    roomName: string
    tipAmount: string
    message: string
    source: string
    tipRoomType: RoomType
    tipType: TipType
    videoMode: string
}

export interface ISendTipResponse {
    success: boolean
    tokenBalance?: number
    error?: string
    tipsInPast24Hours?: number
    showPurchaseLink?: boolean
    chatursafeFlaggedCategories?: string[]
    originalMessage?: IPrivateMessage
}

interface ChatursafeFlaggedTipError extends IPMError {
    chatursafeFlaggedCategories: string[]
    originalMessage: IPrivateMessage
}

export const isChatursafeFlaggedTipError = (
    error: IPMError | number,
): error is ChatursafeFlaggedTipError => {
    return typeof error === "object" && Boolean(
        error.chatursafeFlaggedCategories &&
        error.chatursafeFlaggedCategories.length > 0 &&
        error.originalMessage,
    )
}

export interface ICurrentTokenResponse {
    tokenBalance: number
    tipOptions?: ITipOptions
}

export interface ITipOptions {
    label: string
    options: { label: string }[]
}

export const tipMessageMargin = 6

export function parseCurrentTokensResponse(response: string): ICurrentTokenResponse {
    const p = new ArgJSONMap(response)
    const currentTokensResponse = { tokenBalance: p.getNumber("token_balance") } as ICurrentTokenResponse
    const rawTipOptions = p.getStringOrUndefined("tip_options")
    if (rawTipOptions !== undefined) {
        currentTokensResponse.tipOptions = parseTipOptions(rawTipOptions)
    }
    p.logUnusedDebugging("parseCurrentTokensResponse")
    return currentTokensResponse
}

export function parseSendTipResponse(response: string): ISendTipResponse {
    const p = new ArgJSONMap(response)
    const sendTipResponse = { success: p.getBoolean("success") } as ISendTipResponse
    const error = p.getStringOrUndefined("error", false)
    if (error !== undefined) {
        sendTipResponse.error = error
        sendTipResponse.showPurchaseLink = p.getBoolean("show_purchase_tokens", false, false)

        // Handle chatursafe flagged categories (using snake_case like PM implementation)
        const flaggedCategories = p.getStringListOrUndefined("chatursafe_flagged_categories")
        if (flaggedCategories && flaggedCategories.length > 0) {
            sendTipResponse.chatursafeFlaggedCategories = flaggedCategories

            // Parse original message using same logic as sendPrivateMessage
            const msgObj = p.getObjectOrUndefined("message")
            const userUid = pageContext.current.loggedInUser?.userUid
            if (msgObj && userUid !== undefined) {
                // Parse message using UserMessageTopic like in sendPrivateMessage
                addBaseTopicFields(msgObj as Record<string, unknown>)
                const topic = new UserMessageTopic(userUid)
                const parsedMsg = topic.parseData(new ArgJSONMap(msgObj))
                sendTipResponse.originalMessage = parsedMsg
            }
        }
    } else {
        sendTipResponse.tokenBalance = p.getNumber("token_balance")
        sendTipResponse.tipsInPast24Hours = p.getNumber("tipped_performer_last_24hrs")
    }
    p.logUnusedDebugging("parseSendTipResponse")
    return sendTipResponse
}

function parseOptions(subTipOptions: string): { label: string }[] {
    const tipOpions = []
    for (const option of JSON.parse(subTipOptions)) {
        const p = new ArgJSONMap(JSON.stringify(option))
        tipOpions.push({ label: p.getAsString("label") })
        p.logUnusedDebugging("parseOptions")
    }
    return tipOpions
}

export function parseTipOptions(rawTipOptions: string): ITipOptions | undefined {
    let p = new ArgJSONMap(rawTipOptions)
    const subTipOptions = p.getObjectStringOrUndefined("tip_options")
    if (subTipOptions === undefined) {
        return
    }
    p = new ArgJSONMap(subTipOptions)
    const tipOptions = {
        label: p.getAsString("label"),
        options: parseOptions(p.getObjectString("options")),
    }
    p.logUnusedDebugging("parseTipOptions")
    return tipOptions
}

export function getTokenBalance(roomName: string): Promise<ICurrentTokenResponse> {
    return new Promise((resolve, reject) => {
        getCb(`tipping/current_tokens/?room=${roomName}`).then((xhr) => {
            const tokenResponse = parseCurrentTokensResponse(xhr.responseText)
            tokenBalanceUpdate.fire({ tokens: tokenResponse.tokenBalance })
            resolve(tokenResponse)
        }).catch((xhr) => {
            reject(xhr.status)
        })
    })
}

let isTipPending = false

export function sendTip(data: ISendTipData): Promise<ISendTipResponse> {
    return new Promise((resolve, reject) => {
        if (!isTipPending) {
            isTipPending = true
            postCb(`tipping/send_tip/${data.roomName}/`, {
                "tip_amount": data.tipAmount,
                "message": data.message,
                "source": data.source,
                "tip_room_type": data.tipRoomType,
                "tip_type": data.tipType,
                "video_mode": data.videoMode,
                "from_username": pageContext.current.loggedInUser?.username ?? "",
            }).then((xhr) => {
                isTipPending = false
                const sendTipResponse = parseSendTipResponse(xhr.responseText)
                if (sendTipResponse.tokenBalance !== undefined) {
                    tokenBalanceUpdate.fire({ tokens: sendTipResponse.tokenBalance })
                }

                // Handle chatursafe flagged tips by rejecting with ITipError
                if (sendTipResponse.chatursafeFlaggedCategories && sendTipResponse.chatursafeFlaggedCategories.length > 0) {
                    const tipError: ITipError = {
                        username: data.roomName,
                        errorMessage: sendTipResponse.error ?? "Message flagged by ChaturSafe",
                        showPurchaseLink: sendTipResponse.showPurchaseLink ?? false,
                        chatursafeFlaggedCategories: sendTipResponse.chatursafeFlaggedCategories,
                        originalMessage: sendTipResponse.originalMessage,
                    }
                    reject(tipError)
                    return
                }

                resolve(sendTipResponse)
            }).catch((err) => {
                isTipPending = false
                reject(err.xhr.status)
            })
        }
    })
}
